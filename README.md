# Application Streamlit

Ce projet est une application web construite avec Streamlit en Python.

## Installation

1. Clonez ce dépôt
2. Installez les dépendances :
   ```
   pip install streamlit pandas numpy
   ```

## Lancement de l'application

Pour lancer l'application, exécutez la commande suivante :

```
streamlit run app.py
```

L'application sera accessible à l'adresse http://localhost:8501

## Structure du projet

- `app.py` : Fichier principal de l'application
- `README.md` : Documentation du projet

## Fonctionnalités

- Interface utilisateur interactive avec sidebar
- Visualisations (graphiques et cartes)
- Affichage de données tabulaires
- Formulaire de contact
- Onglets pour organiser le contenu

## Intégration future avec l'IA

Cette application est conçue pour être intégrée ultérieurement avec des fonctionnalités d'IA.

## Licence

MIT
