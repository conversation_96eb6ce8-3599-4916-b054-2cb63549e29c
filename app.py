import streamlit as st
import pandas as pd
import numpy as np

# Configuration de la page
st.set_page_config(
    page_title="Mon Application Streamlit",
    page_icon="✨",
    layout="wide",
    initial_sidebar_state="expanded",
)

# Titre principal
st.title("Mon Application Streamlit")
st.subheader("Une interface utilisateur simple et élégante")

# Sidebar
with st.sidebar:
    st.header("Paramètres")
    option = st.selectbox(
        "Choisissez une option",
        ["Option 1", "Option 2", "Option 3"]
    )
    
    slider_value = st.slider("Sélectionnez une valeur", 0, 100, 50)
    
    st.write("Vous avez sélectionné:", option)
    st.write("Valeur du slider:", slider_value)

# Contenu principal
st.header("Contenu principal")

# Onglets
tab1, tab2, tab3 = st.tabs(["Visualisation", "Données", "À propos"])

with tab1:
    st.header("Visualisation")
    
    # Exemple de graphique
    chart_data = pd.DataFrame(
        np.random.randn(20, 3),
        columns=['A', 'B', 'C']
    )
    
    st.line_chart(chart_data)
    
    # Carte
    st.subheader("Carte")
    map_data = pd.DataFrame(
        np.random.randn(1000, 2) / [50, 50] + [48.8566, 2.3522],  # Paris
        columns=['lat', 'lon']
    )
    
    st.map(map_data)

with tab2:
    st.header("Données")
    
    # Exemple de tableau de données
    df = pd.DataFrame({
        'Première colonne': [1, 2, 3, 4],
        'Deuxième colonne': [10, 20, 30, 40]
    })
    
    st.dataframe(df)
    
    # Téléchargement de fichier
    st.download_button(
        label="Télécharger les données en CSV",
        data=df.to_csv().encode('utf-8'),
        file_name='donnees.csv',
        mime='text/csv',
    )

with tab3:
    st.header("À propos")
    st.write("""
    Cette application est un exemple de ce que vous pouvez créer avec Streamlit.
    
    Streamlit est un framework Python qui permet de créer facilement des applications web interactives
    pour la data science et le machine learning.
    """)
    
    # Expander pour plus d'informations
    with st.expander("En savoir plus sur Streamlit"):
        st.write("""
        Streamlit transforme les scripts de data science en applications web partageables en quelques minutes.
        Tout est en Python pur, et il n'y a pas besoin de connaître HTML, CSS ou JavaScript.
        
        Pour plus d'informations, visitez [streamlit.io](https://streamlit.io).
        """)

# Formulaire
st.header("Formulaire de contact")
with st.form("contact_form"):
    col1, col2 = st.columns(2)
    
    with col1:
        nom = st.text_input("Nom")
    
    with col2:
        email = st.text_input("Email")
    
    message = st.text_area("Message")
    
    submitted = st.form_submit_button("Envoyer")
    
    if submitted:
        st.success(f"Merci {nom} ! Votre message a été envoyé.")
        st.balloons()

# Footer
st.markdown("---")
st.caption("© 2023 Mon Application Streamlit - Tous droits réservés")
