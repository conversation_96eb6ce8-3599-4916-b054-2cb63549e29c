# -*- coding: utf-8 -*-
"""
Created on Mon May 12 11:47:08 2025

@author: 3D
"""

# front1.py
import streamlit as st
import sys
from pathlib import Path
import os
from PyPDF2 import PdfReader

# Portabilité du chemin
APP_DIR = Path(__file__).parent.resolve()
BACKEND_PATH = APP_DIR

# Ajout du chemin backend au sys.path
sys.path.insert(0, str(BACKEND_PATH))

# Import backend
from back_V4 import rag_system
rag_system = rag_system(ttl_seconds=300)

# Configuration de la page Streamlit (placée en premier)
st.set_page_config(page_title="Assistant RAG", layout="wide")

# Styles CSS personnalisés
st.markdown("""
    <style>
    .main-title {
        font-size: 2.5rem;
        color: #4CAF50;
        text-align: center;
        margin-bottom: 1rem;
    }
    .section-title {
        font-size: 1.5rem;
        color: #2196F3;
        margin-top: 1rem;
    }
    .info-box {
        background-color: #E3F2FD;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
    }
    .question-display {
        font-size: 1.2rem;
        font-weight: bold;
        color: #FF5722;
        margin-top: 1rem;
    }
    </style>
""", unsafe_allow_html=True)

# Titre principal
st.markdown('<h1 class="main-title">Assistant RAG pour Ressources humaines 🇫🇷</h1>', unsafe_allow_html=True)

# Aperçu PDF
directory = Path("C:/temp/pdf")
all_pdfs = list(directory.glob("*.pdf"))
if all_pdfs:
    st.markdown('<h2 class="section-title">Aperçu des documents PDF</h2>', unsafe_allow_html=True)
    pdf_choice = st.selectbox("Choisissez un fichier PDF :", all_pdfs, format_func=lambda x: x.name)
    try:
        with open(pdf_choice, "rb") as f:
            reader = PdfReader(f)
            preview_text = "\n\n".join([page.extract_text() for page in reader.pages[:2] if page.extract_text()])
        st.text_area("Aperçu des 2 premières pages :", preview_text, height=300)
    except Exception as e:
        st.error(f"Erreur lors de la lecture du PDF : {e}")

# Instructions utilisateur
st.markdown("""
    <div class="info-box">
        <strong>Comment utiliser cet assistant :</strong><br>
        1. Saisissez votre question ou choisissez une question prédéfinie.<br>
        2. Sélectionnez votre profil (CAPBP, Ville de Pau, CCAS, élus).<br>
        3. Ajustez les paramètres de génération si nécessaire.<br>
        4. Cliquez sur "Obtenir la réponse".<br>
        5. Utilisez "Quitter" pour arrêter l'application.
    </div>
""", unsafe_allow_html=True)

# Sidebar options
st.sidebar.header("Options")
if st.sidebar.button("Quitter"):
    st.sidebar.info("Merci d'avoir utilisé l’assistant RAG. À bientôt !")
    st.stop()

if st.sidebar.button("Vider les caches"):
    try:
        rag_system.response_cache.clear()
        rag_system.embedding_cache.clear()
        st.sidebar.success("Caches vidés avec succès !")
    except AttributeError:
        st.sidebar.error("Impossible d'accéder aux caches. Vérifiez l'implémentation dans back_V1.py")

# Paramètres de génération
st.sidebar.header("Paramètres de génération")
temperature = st.sidebar.slider("Température", min_value=0.1, max_value=1.0, value=0.4, step=0.1)
top_p = st.sidebar.slider("Top P", min_value=0.1, max_value=1.0, value=0.90, step=0.01)
top_k = st.sidebar.slider("Top K", min_value=1, max_value=100, value=60, step=5)
max_new_tokens = st.sidebar.number_input("Max New Tokens", min_value=300, max_value=2000, value=1600, step=50)

# Questions prédéfinies
qa_pairs = [
    ("Je vais à Paris 3 jours 2 nuits à l'hotel en déplacement en avion pour une formation  comment je suis remboursé?", "CCAS de Pau"),
    ("Quels sont les taux de remboursement pour mes des frais de déplacement pour une formation CNFPT?", "Ville de Pau"),
    ("Comment faire une demande d'avance pour mes frais de déplacement?", "Communauté d'Agglomération Pau Béarn Pyrénées (CAPBP)"),
    ("Comment faire une demande d'avance pour mes frais de déplacement?", "CCAS de Pau"),
]
selected_idx = st.sidebar.selectbox(
    "Choisissez une question prédéfinie :",
    options=range(len(qa_pairs)),
    format_func=lambda x: qa_pairs[x][0]
)

st.markdown('<h2 class="section-title">Posez votre question</h2>', unsafe_allow_html=True)

user_question = st.text_input("Votre question (laisser vide pour la prédéfinie) :")
profil_options = [
    "Communauté d'Agglomération Pau Béarn Pyrénées (CAPBP)",
    "CCAS de Pau",
    "Ville de Pau",
    "Elus"
]
profil_libelle = st.selectbox("Votre profil :", profil_options)

question = user_question.strip() or qa_pairs[selected_idx][0]

st.markdown(f'<p class="question-display">Question posée : {question}</p>', unsafe_allow_html=True)

if st.button("Obtenir la réponse"):
    try:
        with st.spinner("En cours de réflexion, merci de patienter…"):
            answer = rag_system.answer_question(
                question,
                profil_libelle,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                max_new_tokens=max_new_tokens
            )

        st.success("Réponse générée avec succès !")
        cleaned = answer.replace('<!-- image -->', '').replace('<!--', '').replace('-->', '').strip() if answer else None

        st.markdown("### Réponse")
        if cleaned:
            sections = cleaned.split("Réponse :")
            if len(sections) > 1:
                context_part = sections[0].strip()
                response_part = sections[1].strip()
                st.markdown("#### Contexte")
                for line in context_part.split("\n"):
                    line = line.strip()
                    if line.startswith("Profil:"):
                        st.markdown(f"**Profil** : {line.replace('Profil:', '').strip()}")
                    elif line and not line.startswith("Question:"):
                        if ":" in line and len(line.split(":")) == 2:
                            key, value = line.split(":", 1)
                            st.markdown(f"- **{key.strip()}** : {value.strip()}")
                        else:
                            st.markdown(f"- {line}")
                st.markdown("---")
                st.markdown("#### Détails du remboursement")
                for para in response_part.split("\n\n"):
                    if para.strip():
                        st.markdown(para.strip())
            else:
                for para in cleaned.split("\n\n"):
                    if para.strip():
                        st.markdown(para.strip())
        else:
            st.error("Aucune réponse n'a été générée par le modèle.")
    except Exception as e:
        st.error(f"Erreur lors de la génération de la réponse : {e}")

st.sidebar.info("Ce système utilise un modèle RAG pour répondre à vos questions basées sur des documents.")
