# Assistant RH RAG pour Ressources Humaines

Cette application Streamlit fournit un assistant intelligent pour les questions RH, basé sur un système RAG (Retrieval-Augmented Generation) qui utilise le modèle Mistral-7B-Instruct pour générer des réponses précises et contextuelles.

## Fonctionnalités

- **Interface utilisateur intuitive** : Interface Streamlit moderne et conviviale
- **Système RAG** : Recherche dans une base de documents PDF pour fournir des réponses contextuelles
- **Personnalisation par profil** : Adapte les réponses selon le profil de l'utilisateur (CAPBP, Ville de Pau, CCAS, élus)
- **Questions prédéfinies** : Ensemble de questions courantes pour un démarrage rapide
- **Visualisation des sources** : Affiche les documents consultés pour générer la réponse
- **Historique des questions** : Conserve l'historique des questions et réponses pendant la session
- **Métriques de performance** : Affiche le temps d'exécution et le nombre de tokens générés

## Prérequis

- Python 3.8+
- PyTorch
- Transformers
- Sentence-Transformers
- Streamlit
- PyPDF2

## Installation

1. Clonez ce dépôt
2. Installez les dépendances :
   ```
   pip install -r requirements.txt
   ```

## Configuration

L'application utilise les chemins suivants par défaut :
- `C:/temp/FILE_RAG` : Répertoire de base pour les fichiers RAG
- `C:/temp/FILE_RAG/embeddings_cache.pt` : Cache des embeddings
- `C:/temp/FILE_RAG/output/lint_pdf_chunked_ALL.json` : Fichier JSON contenant les chunks de documents
- `C:/temp/pdf` : Répertoire contenant les fichiers PDF

Vous pouvez modifier ces chemins en définissant la variable d'environnement `BASE_PATH`.

## Utilisation

1. Lancez l'application :
   ```
   streamlit run app_rh.py
   ```

2. Dans l'interface :
   - Chargez le modèle et les embeddings depuis la barre latérale
   - Saisissez votre question ou choisissez une question prédéfinie
   - Sélectionnez votre profil (CAPBP, Ville de Pau, CCAS, élus)
   - Ajustez les paramètres de génération si nécessaire
   - Cliquez sur "Obtenir la réponse"
   - Consultez la réponse et les sources utilisées

## Structure du code

- `app_rh.py` : Application Streamlit principale
- `TravauxV6.py` : Backend contenant la logique RAG et le modèle de génération

## Paramètres de génération

- **Température** : Contrôle la créativité des réponses (0.1-1.0)
- **Top P** : Contrôle la diversité des réponses (0.1-1.0)
- **Top K** : Nombre de tokens les plus probables à considérer (1-100)
- **Max New Tokens** : Nombre maximum de tokens à générer (300-2000)

## Améliorations futures

- Intégration avec une base de données pour stocker l'historique des questions/réponses
- Ajout d'un système de feedback utilisateur pour améliorer les réponses
- Optimisation des performances pour les appareils à ressources limitées
- Ajout de fonctionnalités d'exportation des réponses en PDF
- Intégration avec des systèmes d'authentification

## Licence

Ce projet est sous licence propriétaire et destiné à un usage interne uniquement.

## Auteurs

Développé pour la Ville de Pau, CCAS et CAPBP.
