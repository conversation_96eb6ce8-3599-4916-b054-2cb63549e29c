# -*- coding: utf-8 -*-
"""
Module pour afficher et gérer les PDF dans l'application Minute RH
"""

import streamlit as st
import os
from pathlib import Path
import webbrowser
import logging
from PyPDF2 import PdfReader
import re
import base64
from streamlit.components.v1 import html

# Configuration du logging
logger = logging.getLogger(__name__)

class PdfViewer:
    """Classe pour gérer l'affichage et l'ouverture des PDF"""

    def __init__(self, pdf_directory=None):
        """
        Initialise le visualiseur de PDF

        Args:
            pdf_directory (Path, optional): Répertoire contenant les PDF.
                                           Si None, utilise le répertoire courant/PDF
        """
        if pdf_directory is None:
            self.pdf_directory = Path(os.path.dirname(os.path.abspath(__file__))) / "PDF"
        else:
            self.pdf_directory = Path(pdf_directory)

        # Vérifier que le répertoire existe
        if not self.pdf_directory.exists():
            logger.warning(f"Le répertoire PDF {self.pdf_directory} n'existe pas")
            os.makedirs(self.pdf_directory, exist_ok=True)
            logger.info(f"Répertoire PDF créé: {self.pdf_directory}")

        # Charger la liste des PDF disponibles
        self.refresh_pdf_list()

    def refresh_pdf_list(self):
        """Rafraîchit la liste des PDF disponibles"""
        self.pdf_files = list(self.pdf_directory.glob("*.pdf"))
        logger.info(f"Trouvé {len(self.pdf_files)} fichiers PDF dans {self.pdf_directory}")
        return self.pdf_files

    def find_pdf_by_name(self, file_name):
        """
        Trouve le fichier PDF correspondant au nom de fichier mentionné dans les sources

        Args:
            file_name (str): Nom du fichier à rechercher

        Returns:
            Path: Chemin vers le fichier PDF ou None si non trouvé
        """
        # Supprimer l'extension .txt si présente
        base_name = file_name.replace('.txt', '')

        # Normaliser le nom (supprimer les caractères spéciaux, mettre en minuscules)
        base_name = re.sub(r'[^\w\s]', '', base_name).lower()

        # Chercher dans le répertoire PDF
        for pdf_file in self.pdf_files:
            # Normaliser le nom du PDF
            pdf_name = re.sub(r'[^\w\s]', '', pdf_file.stem).lower()

            # Vérifier si le nom du PDF correspond au nom du fichier source
            if base_name in pdf_name or pdf_name in base_name:
                logger.info(f"PDF trouvé pour {file_name}: {pdf_file}")
                return pdf_file

        logger.warning(f"Aucun PDF trouvé pour {file_name}")
        return None

    def display_pdf_in_app(self, pdf_file):
        """
        Affiche un fichier PDF directement dans l'application Streamlit

        Args:
            pdf_file (Path): Chemin vers le fichier PDF à afficher

        Returns:
            bool: True si le PDF a été affiché avec succès, False sinon
        """
        try:
            # Méthode 1: Utiliser un objet HTML avec embed
            pdf_path = pdf_file.absolute().as_posix()
            pdf_display = f'''
                <object
                    data="file://{pdf_path}"
                    type="application/pdf"
                    width="100%"
                    height="800px"
                    style="border: 1px solid #ddd; border-radius: 5px;"
                >
                    <p>Il semble que votre navigateur ne puisse pas afficher le PDF.
                    <a href="file://{pdf_path}" target="_blank">Cliquez ici pour le télécharger</a>.</p>
                </object>
            '''

            # Afficher le PDF
            st.markdown(pdf_display, unsafe_allow_html=True)

            # Méthode 2: Afficher un lien direct vers le PDF
            st.markdown(f"**Si le PDF ne s'affiche pas correctement, [cliquez ici pour l'ouvrir](file://{pdf_path})**", unsafe_allow_html=True)

            # Méthode 3: Afficher un bouton pour ouvrir le PDF dans le navigateur
            if st.button("Ouvrir dans le navigateur externe", key=f"ext_btn_{pdf_file.name}"):
                self.open_pdf(pdf_file)

            logger.info(f"PDF affiché dans l'application: {pdf_file.name}")
            return True
        except Exception as e:
            logger.error(f"Erreur lors de l'affichage du PDF {pdf_file.name}: {e}")
            st.error(f"Erreur lors de l'affichage du PDF: {e}")

            # En cas d'erreur, proposer d'ouvrir le PDF dans le navigateur
            st.warning("Tentative d'affichage du PDF dans le navigateur externe...")
            self.open_pdf(pdf_file)

            return False

    def open_pdf_in_new_tab(self, pdf_file):
        """
        Ouvre un fichier PDF dans un nouvel onglet du navigateur en utilisant JavaScript

        Args:
            pdf_file (Path): Chemin vers le fichier PDF à ouvrir

        Returns:
            bool: True si le code JavaScript a été généré avec succès
        """
        try:
            # Créer un lien absolu vers le fichier PDF
            pdf_path = f"file:///{pdf_file.absolute()}"

            # Script JavaScript pour ouvrir le PDF dans un nouvel onglet
            script = f"""
            <script>
                window.open("{pdf_path}", "_blank");
            </script>
            """

            # Exécuter le script JavaScript
            html(script)

            logger.info(f"PDF ouvert dans un nouvel onglet: {pdf_file.name}")
            return True
        except Exception as e:
            logger.error(f"Erreur lors de l'ouverture du PDF dans un nouvel onglet: {e}")
            return False

    def open_pdf(self, pdf_file):
        """
        Ouvre un fichier PDF en utilisant l'application par défaut du système

        Args:
            pdf_file (Path): Chemin vers le fichier PDF à ouvrir

        Returns:
            bool: True si le PDF a été ouvert avec succès, False sinon
        """
        try:
            # Méthode 1: Utiliser webbrowser.open() qui est plus fiable que open_new_tab
            pdf_path = f"file:///{pdf_file.absolute()}"
            webbrowser.open(pdf_path)

            # Méthode 2: Utiliser os.startfile sur Windows (plus direct)
            if os.name == 'nt':  # Windows
                try:
                    os.startfile(pdf_file)
                    logger.info(f"PDF ouvert avec os.startfile: {pdf_file.name}")
                except Exception as e:
                    logger.warning(f"Erreur avec os.startfile: {e}, utilisation de webbrowser.open")

            logger.info(f"PDF ouvert: {pdf_file.name}")
            return True
        except Exception as e:
            logger.error(f"Erreur lors de l'ouverture du PDF {pdf_file.name}: {e}")

            # Afficher le chemin complet pour aider au débogage
            logger.error(f"Chemin du PDF: {pdf_file.absolute()}")
            return False

    def get_pdf_preview(self, pdf_file, max_pages=2):
        """
        Obtient un aperçu du contenu d'un fichier PDF

        Args:
            pdf_file (Path): Chemin vers le fichier PDF
            max_pages (int, optional): Nombre maximum de pages à extraire. Par défaut 2.

        Returns:
            str: Texte extrait des premières pages du PDF
        """
        try:
            with open(pdf_file, "rb") as f:
                reader = PdfReader(f)
                preview_text = "\n\n".join([
                    page.extract_text()
                    for page in reader.pages[:max_pages]
                    if page.extract_text()
                ])
            return preview_text
        except Exception as e:
            logger.error(f"Erreur lors de la lecture du PDF {pdf_file.name}: {e}")
            return f"Erreur lors de la lecture du PDF: {e}"

    def display_pdf_selector(self):
        """
        Affiche un sélecteur de PDF dans la barre latérale Streamlit

        Returns:
            Path: Le fichier PDF sélectionné ou None si aucun PDF n'est disponible
        """
        if not self.pdf_files:
            st.sidebar.warning("Aucun fichier PDF disponible")
            return None

        st.sidebar.markdown('<h3 class="section-title">Documents PDF disponibles</h3>', unsafe_allow_html=True)
        pdf_choice = st.sidebar.selectbox(
            "Choisissez un fichier PDF :",
            self.pdf_files,
            format_func=lambda x: x.name
        )

        # Bouton unique pour ouvrir le PDF dans le navigateur
        if st.sidebar.button("📄 Ouvrir dans le navigateur"):
            success = self.open_pdf(pdf_choice)
            if success:
                st.sidebar.success(f"PDF ouvert dans le navigateur")
            else:
                st.sidebar.error(f"Erreur lors de l'ouverture du PDF")

        return pdf_choice

    def create_pdf_viewer_page(self, pdf_file):
        """
        Crée une page HTML simple pour afficher un PDF

        Args:
            pdf_file (Path): Chemin vers le fichier PDF

        Returns:
            str: Chemin vers le fichier HTML créé
        """
        try:
            # Créer un répertoire temporaire s'il n'existe pas
            temp_dir = Path(os.path.dirname(os.path.abspath(__file__))) / "temp"
            os.makedirs(temp_dir, exist_ok=True)

            # Créer un fichier HTML pour afficher le PDF
            html_path = temp_dir / f"{pdf_file.stem}.html"
            pdf_rel_path = os.path.relpath(pdf_file, temp_dir)

            html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>{pdf_file.name}</title>
    <style>
        body, html {{
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }}
        #pdf-viewer {{
            width: 100%;
            height: 100%;
            border: none;
        }}
    </style>
</head>
<body>
    <embed id="pdf-viewer" src="{pdf_rel_path}" type="application/pdf" width="100%" height="100%">
</body>
</html>"""

            with open(html_path, "w", encoding="utf-8") as f:
                f.write(html_content)

            return html_path
        except Exception as e:
            logger.error(f"Erreur lors de la création de la page HTML pour {pdf_file.name}: {e}")
            return None

    def display_sources(self, retrieved_files, container=None):
        """
        Affiche les sources avec des boutons pour ouvrir les PDF correspondants

        Args:
            retrieved_files (list): Liste des noms de fichiers sources
            container (streamlit.container, optional): Conteneur Streamlit où afficher les sources.
                                                     Si None, utilise st directement.
        """
        # Utiliser le conteneur fourni ou st directement
        ctx = container if container is not None else st

        with ctx.expander("Voir les sources consultées"):
            ctx.markdown('<div class="context-box">', unsafe_allow_html=True)

            # Dédupliquer les fichiers
            unique_files = set(retrieved_files)

            for i, file_name in enumerate(unique_files):
                # Trouver le PDF correspondant
                pdf_file = self.find_pdf_by_name(file_name)

                col1, col2 = ctx.columns([4, 1])
                with col1:
                    ctx.markdown(f'<span class="file-reference">{file_name}</span>', unsafe_allow_html=True)

                if pdf_file and pdf_file.exists():
                    with col2:
                        # Utiliser un bouton Streamlit pour ouvrir le PDF
                        if ctx.button(f"📄 Voir PDF", key=f"btn_pdf_{i}_{file_name}"):
                            # Utiliser la même méthode que le bouton "Ouvrir dans le navigateur"
                            success = self.open_pdf(pdf_file)
                            if success:
                                ctx.success(f"PDF ouvert dans le navigateur: {pdf_file.name}")
                            else:
                                ctx.error(f"Erreur lors de l'ouverture du PDF")
                else:
                    with col2:
                        ctx.markdown("❌ PDF non trouvé")

            ctx.markdown('</div>', unsafe_allow_html=True)
            ctx.caption("Ces fichiers sont disponibles dans SharePoint.")


# Exemple d'utilisation si exécuté directement
if __name__ == "__main__":
    st.set_page_config(page_title="Test ViewPdf", layout="wide")

    st.title("Test du module ViewPdf")

    # Créer une instance du visualiseur de PDF
    pdf_viewer = PdfViewer()

    # Afficher le sélecteur de PDF dans la barre latérale
    pdf_viewer.display_pdf_selector()

    # Simuler des fichiers récupérés
    retrieved_files = [
        "Guide Formation CAPBP.txt",
        "Frais_Déplacements_CNFPT.txt",
        "DRH-remboursement_deplacement_concours_examen_hors_agglo.txt"
    ]

    # Afficher les sources
    pdf_viewer.display_sources(retrieved_files)
