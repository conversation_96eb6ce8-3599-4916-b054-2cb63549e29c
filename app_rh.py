# -*- coding: utf-8 -*-
"""
Application Streamlit pour l'Assistant RH basé sur RAG
Intègre le backend TravauxV6.py
"""

import streamlit as st
import sys
from pathlib import Path
import os
import re
import json
import logging
import torch
from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
from PyPDF2 import PdfReader
import time

# Configuration du logging plus détaillé
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Affiche les logs dans la console
        logging.FileHandler("app_rh.log")  # Enregistre les logs dans un fichier
    ]
)
logger = logging.getLogger(__name__)

# Configuration du logging pour les Q&A
qa_logger = logging.getLogger("qa_logger")
qa_logger.setLevel(logging.INFO)
qa_handler = logging.FileHandler("QA.log")
qa_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
qa_logger.addHandler(qa_handler)
qa_logger.propagate = False  # Évite la duplication des logs

# Configuration de la page Streamlit
st.set_page_config(
    page_title="Assistant RH RAG",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Styles CSS personnalisés
st.markdown("""
    <style>
    .main-title {
        font-size: 2.5rem;
        color: #4CAF50;
        text-align: center;
        margin-bottom: 1rem;
    }
    .section-title {
        font-size: 1.5rem;
        color: #2196F3;
        margin-top: 1rem;
    }
    .info-box {
        background-color: #E3F2FD;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
    }
    .question-display {
        font-size: 1.2rem;
        font-weight: bold;
        color: #FF5722;
        margin-top: 1rem;
    }
    .response-box {
        background-color: #F1F8E9;
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 5px solid #4CAF50;
        margin-top: 1rem;
    }
    .context-box {
        background-color: #FFF8E1;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }
    .file-reference {
        color: #0D47A1;
        font-weight: bold;
    }
    .metrics-container {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
    }
    .footer {
        text-align: center;
        margin-top: 2rem;
        color: #757575;
        font-size: 0.8rem;
    }
    </style>
""", unsafe_allow_html=True)

# Titre principal
st.markdown('<h1 class="main-title">Minute RH - Votre assistant RH</h1>', unsafe_allow_html=True)

# Afficher l'information sur le mode d'exécution (CPU/GPU) dans la barre latérale
if 'device' in st.session_state:
    if st.session_state.device == 'cuda':
        st.sidebar.markdown('<div style="text-align:center;padding:5px;background-color:#d4edda;color:#155724;border-radius:5px;margin-bottom:10px;font-size:0.9rem;">🚀 Mode GPU</div>', unsafe_allow_html=True)
    else:
        st.sidebar.markdown('<div style="text-align:center;padding:5px;background-color:#fff3cd;color:#856404;border-radius:5px;margin-bottom:10px;font-size:0.9rem;">💻 Mode CPU</div>', unsafe_allow_html=True)

# Initialisation des variables de session
if 'response_history' not in st.session_state:
    st.session_state.response_history = []
if 'question_history' not in st.session_state:
    st.session_state.question_history = []
if 'model_loaded' not in st.session_state:
    st.session_state.model_loaded = False
if 'embeddings_loaded' not in st.session_state:
    st.session_state.embeddings_loaded = False

# Chemins des fichiers
BASE_PATH = Path(os.getenv("BASE_PATH", "C:/temp/FILE_RAG"))
EMBEDDINGS_CACHE_PATH = BASE_PATH / "embeddings_cache.pt"
CHUNKED_JSON_PATH = BASE_PATH / "output/lint_pdf_chunked_ALL.json"
PDF_DIRECTORY = Path("C:/temp/pdf")

# Fonction pour charger les chunks
@st.cache_data(ttl=3600)
def load_chunks():
    if not CHUNKED_JSON_PATH.exists():
        st.error(f"Fichier de chunks introuvable : {CHUNKED_JSON_PATH}")
        return []
    with open(CHUNKED_JSON_PATH, encoding="utf-8") as f:
        return json.load(f)

# Fonction pour charger le modèle d'embeddings
@st.cache_resource
def load_embedder():
    return SentenceTransformer('distiluse-base-multilingual-cased-v2')

# Fonction pour charger les embeddings
@st.cache_resource
def load_embeddings(all_chunks, _embedder):
    # Utilisation de _embedder pour éviter que Streamlit ne tente de le hacher
    if EMBEDDINGS_CACHE_PATH.exists():
        # Utiliser weights_only=True pour éviter les avertissements de sécurité
        chunk_embeddings = torch.load(EMBEDDINGS_CACHE_PATH, weights_only=True)
        if len(chunk_embeddings) != len(all_chunks):
            with st.spinner("Recalcul des embeddings en cours..."):
                chunk_texts = [chunk["text"] for chunk in all_chunks]
                chunk_embeddings = _embedder.encode(chunk_texts, convert_to_tensor=True)
                torch.save(chunk_embeddings, EMBEDDINGS_CACHE_PATH)
    else:
        with st.spinner("Calcul des embeddings en cours..."):
            chunk_texts = [chunk["text"] for chunk in all_chunks]
            chunk_embeddings = _embedder.encode(chunk_texts, convert_to_tensor=True)
            torch.save(chunk_embeddings, EMBEDDINGS_CACHE_PATH)
    return chunk_embeddings

# Fonction pour charger le modèle et le tokenizer avec détection automatique GPU/CPU
@st.cache_resource
def load_model_and_tokenizer():
    MODEL_NAME = "mistralai/Mistral-7B-Instruct-v0.3"
    HF_TOKEN = "*************************************"

    with st.spinner("Chargement du modèle et du tokenizer..."):
        # Vérifier si un GPU NVIDIA est disponible
        if torch.cuda.is_available():
            logger.info("GPU NVIDIA détecté. Utilisation du GPU pour l'inférence.")
            device = "cuda"
            torch_dtype = torch.float16  # Utiliser float16 pour GPU (plus rapide et moins de mémoire)
            st.success("🚀 GPU NVIDIA détecté! L'inférence sera plus rapide.")
        else:
            logger.info("Aucun GPU NVIDIA détecté. Utilisation du CPU pour l'inférence.")
            device = "cpu"
            torch_dtype = torch.float32  # Utiliser float32 pour CPU (plus stable)
            st.warning("⚠️ Aucun GPU NVIDIA détecté. L'inférence sera plus lente sur CPU.")

        # Stocker l'information sur le device dans la session
        st.session_state.device = device

        # Charger le tokenizer
        tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME, token=HF_TOKEN)

        # Charger le modèle avec les paramètres appropriés
        model = AutoModelForCausalLM.from_pretrained(
            MODEL_NAME,
            token=HF_TOKEN,
            torch_dtype=torch_dtype,
            device_map="auto",  # Laisser accelerate gérer l'allocation des appareils
            low_cpu_mem_usage=True if device == "cpu" else False  # Optimisation pour CPU uniquement si nécessaire
        )

        logger.info(f"Modèle chargé avec succès sur {device.upper()}")
    return model, tokenizer

# Fonction pour formater le contexte avec les noms de fichiers
def format_context_with_filenames(chunks):
    context = ""
    for chunk in chunks:
        context += f"Fichier: {chunk['file_name']} (chunk {chunk['chunk_id']}):\n{chunk['text']}\n\n"
    return context.strip()

# Fonction pour répondre à une question
def answer_question(
    question,
    profil_libelle,
    embedder,
    all_chunks,
    chunk_embeddings,
    model,
    tokenizer,
    temperature=0.2,
    top_p=0.85,
    top_k=100,
    max_new_tokens=800
):
    start_time = time.time()

    # Calculer l'embedding de la question
    question_embedding = embedder.encode(question, convert_to_tensor=True)

    # Calculer les similarités cosinus
    similarities = torch.cosine_similarity(question_embedding, chunk_embeddings)

    # Récupérer les indices des top-3 chunks
    top_k_indices = torch.topk(similarities, k=3).indices

    # Récupérer les top-3 chunks
    retrieved_chunks = [all_chunks[i] for i in top_k_indices]

    # Nettoyer les chunks (virer <!-- image -->)
    cleaned_chunks = []
    for chunk in retrieved_chunks:
        text = chunk["text"]
        text = re.sub(r'<!-- image -->', '', text)
        cleaned_chunks.append({
            "file_name": chunk["file_name"],
            "chunk_id": chunk["chunk_id"],
            "text": text
        })

    # Formater le contexte
    formatted_context = format_context_with_filenames(cleaned_chunks)

    # Créer le prompt
    prompt = f"""Tu es un expert RH pour les agents de la Ville de Pau, du CCAS ou de la CAPBP.
    Ton rôle est de fournir des informations précises et adaptées sur les droits et procédures (déplacements, frais, primes, maladie, accidents de service, etc.).

Profil de l'agent : {profil_libelle}

Contexte pertinent :
{formatted_context}

Question : {question}

Réponds en français, de manière claire et synthétique.
 Base ta réponse uniquement sur le contexte fourni et adapte-la au profil de l'agent.
 Si une information est manquante, indique-le clairement.
 Évite les redondances
 Structure ta réponse avec des puces ou des paragraphes courts, et cite les fichiers PDF pertinents du contexte.
 informe que les fichiers sont dans Sharepoint


Réponse :
"""

    # Générer la réponse
    pipe = pipeline(
        "text-generation",
        model=model,
        tokenizer=tokenizer,
        return_full_text=False
        # Ne pas spécifier device car le modèle est déjà chargé avec accelerate
    )
    result = pipe(
        prompt,
        max_new_tokens=max_new_tokens,
        temperature=temperature,
        top_p=top_p,
        top_k=top_k,
        do_sample=True
    )
    answer = result[0]['generated_text'].strip()

    # Compter les tokens
    encoded = tokenizer(answer, return_tensors="pt")
    token_count = encoded.input_ids.shape[-1]

    # Calculer le temps d'exécution
    execution_time = time.time() - start_time

    return {
        "answer": answer,
        "token_count": token_count,
        "execution_time": execution_time,
        "context": formatted_context,
        "retrieved_files": [chunk["file_name"] for chunk in cleaned_chunks]
    }

# Sidebar
st.sidebar.header("Options")

# Bouton pour charger le modèle
if not st.session_state.model_loaded:
    if st.sidebar.button("Charger le modèle"):
        try:
            logger.info("Début du chargement du modèle et du tokenizer...")
            model, tokenizer = load_model_and_tokenizer()
            logger.info("Modèle et tokenizer chargés avec succès")

            # Stocker les variables dans la session
            st.session_state.model = model
            st.session_state.tokenizer = tokenizer
            st.session_state.model_loaded = True
            st.sidebar.success("Modèle chargé avec succès !")
            logger.info("Modèle et tokenizer stockés dans la session avec succès")
        except Exception as e:
            error_msg = f"Erreur lors du chargement du modèle : {e}"
            logger.error(error_msg)
            logger.exception("Détails de l'erreur:")
            st.sidebar.error(error_msg)
else:
    st.sidebar.success("Modèle chargé et prêt à l'emploi !")

# Bouton pour charger les embeddings
if not st.session_state.embeddings_loaded:
    if st.sidebar.button("Charger les embeddings"):
        try:
            logger.info("Début du chargement des chunks...")
            all_chunks = load_chunks()
            logger.info(f"Chunks chargés avec succès : {len(all_chunks)} chunks trouvés")

            logger.info("Chargement du modèle d'embeddings...")
            embedder = load_embedder()
            logger.info("Modèle d'embeddings chargé avec succès")

            logger.info("Calcul ou chargement des embeddings...")
            chunk_embeddings = load_embeddings(all_chunks, embedder)
            logger.info(f"Embeddings chargés avec succès : {chunk_embeddings.shape}")

            # Stocker les variables dans la session
            st.session_state.all_chunks = all_chunks
            st.session_state.embedder = embedder
            st.session_state.chunk_embeddings = chunk_embeddings
            st.session_state.embeddings_loaded = True
            st.sidebar.success("Embeddings chargés avec succès !")
            logger.info("Toutes les variables stockées dans la session avec succès")
        except Exception as e:
            error_msg = f"Erreur lors du chargement des embeddings : {e}"
            logger.error(error_msg)
            logger.exception("Détails de l'erreur:")
            st.sidebar.error(error_msg)
else:
    st.sidebar.success("Embeddings chargés et prêts à l'emploi !")

# Bouton pour quitter
if st.sidebar.button("Quitter"):
    st.sidebar.info("Merci d'avoir utilisé l'assistant RH RAG. À bientôt !")
    st.stop()

# Paramètres de génération
st.sidebar.header("Paramètres de génération")
temperature = st.sidebar.slider("Température", min_value=0.1, max_value=1.0, value=0.4, step=0.1)
top_p = st.sidebar.slider("Top P", min_value=0.1, max_value=1.0, value=0.90, step=0.01)
top_k = st.sidebar.slider("Top K", min_value=1, max_value=100, value=60, step=5)

# Ajuster la plage de max_new_tokens en fonction du device (CPU/GPU)
if 'device' in st.session_state and st.session_state.device == 'cuda':
    # Sur GPU, permettre des réponses plus longues
    max_new_tokens = st.sidebar.number_input("Max New Tokens", min_value=100, max_value=3000, value=1600, step=100)
else:
    # Sur CPU, suggérer des valeurs plus petites pour des tests plus rapides
    max_new_tokens = st.sidebar.number_input("Max New Tokens", min_value=10, max_value=3000, value=800, step=100,
                                            help="Valeur plus petite recommandée pour CPU. Augmentez pour des réponses plus détaillées mais plus lentes.")

# Questions prédéfinies
qa_pairs = [
    ("Je vais à Paris 3 jours 2 nuits à l'hotel en déplacement en avion pour une formation, comment je suis remboursé?", "CCAS de Pau"),
    ("Quels sont les taux de remboursement pour mes des frais de déplacement pour une formation CNFPT?", "Ville de Pau"),
    ("Comment faire une demande d'avance pour mes frais de déplacement?", "Communauté d'Agglomération Pau Béarn Pyrénées (CAPBP)"),
    ("Comment faire une demande d'avance pour mes frais de déplacement?", "CCAS de Pau"),
    ("Est-ce que je suis aidé si je viens au travail en vélo ?", "CAPBP"),
    ("J'ai deux enfants de 8 et 15 ans, ai-je droit à une prime? Si oui combien? Je suis catégorie B", "CAPBP"),
    ("Je fais grève une semaine, est-ce que cela coupe mon salaire, de combien?", "Ville de Pau"),
    ("Quels sont les droits et devoirs des fonctionnaires dans la CAPBP?", "CAPBP"),
]

selected_idx = st.sidebar.selectbox(
    "Choisissez une question prédéfinie :",
    options=range(len(qa_pairs)),
    format_func=lambda x: qa_pairs[x][0]
)

# Aperçu PDF
if PDF_DIRECTORY.exists():
    all_pdfs = list(PDF_DIRECTORY.glob("*.pdf"))
    if all_pdfs:
        st.sidebar.markdown('<h3 class="section-title">Documents PDF disponibles</h3>', unsafe_allow_html=True)
        pdf_choice = st.sidebar.selectbox("Choisissez un fichier PDF :", all_pdfs, format_func=lambda x: x.name)

        col1, col2 = st.sidebar.columns(2)

        with col1:
            if st.button("Afficher l'aperçu"):
                try:
                    with open(pdf_choice, "rb") as f:
                        reader = PdfReader(f)
                        preview_text = "\n\n".join([page.extract_text() for page in reader.pages[:2] if page.extract_text()])
                    st.markdown('<h2 class="section-title">Aperçu du document PDF</h2>', unsafe_allow_html=True)
                    st.text_area("Aperçu des 2 premières pages :", preview_text, height=300)
                except Exception as e:
                    st.error(f"Erreur lors de la lecture du PDF : {e}")

        with col2:
            if st.button("Ouvrir dans le navigateur"):
                try:
                    # Créer un lien absolu vers le fichier PDF
                    pdf_path = f"file:///{pdf_choice.absolute()}"
                    # Ouvrir le PDF dans un nouvel onglet du navigateur
                    import webbrowser
                    webbrowser.open_new_tab(pdf_path)
                    st.sidebar.success(f"PDF ouvert dans un nouvel onglet")
                    logger.info(f"PDF ouvert dans le navigateur: {pdf_choice.name}")
                except Exception as e:
                    st.sidebar.error(f"Erreur lors de l'ouverture du PDF : {e}")
                    logger.error(f"Erreur lors de l'ouverture du PDF : {e}")

# Instructions utilisateur
st.markdown("""
    <div class="info-box">
        <strong>Comment utiliser cet assistant :</strong><br>
        <span style="font-size:0.9rem; color:black;">
        1. Chargez le modèle et les embeddings depuis la barre latérale.<br>
        2. Saisissez votre question ou choisissez une question prédéfinie.<br>
        3. Sélectionnez votre profil (CAPBP, Ville de Pau, CCAS, élus).<br>
        4. Ajustez les paramètres de génération si nécessaire.<br>
        &nbsp;&nbsp;&nbsp;• <em>Sur CPU</em>: utilisez des valeurs plus petites pour "Max New Tokens" (10-300) pour des tests rapides<br>
        &nbsp;&nbsp;&nbsp;• <em>Sur GPU</em>: vous pouvez utiliser des valeurs plus élevées (jusqu'à 3000) sans perte significative de performance<br>
        5. Cliquez sur "Obtenir la réponse".<br>
        6. Consultez l'historique de vos questions et réponses.
        </span>
    </div>
""", unsafe_allow_html=True)

# Section principale pour poser une question
st.markdown('<h2 class="section-title">Posez votre question</h2>', unsafe_allow_html=True)

user_question = st.text_input("Votre question (laisser vide pour utiliser la question prédéfinie) :")
profil_options = [
    "Communauté d'Agglomération Pau Béarn Pyrénées (CAPBP)",
    "CCAS de Pau",
    "Ville de Pau",
    "Elus"
]
profil_libelle = st.selectbox("Votre profil :", profil_options)

question = user_question.strip() or qa_pairs[selected_idx][0]

st.markdown(f'<p class="question-display">Question posée : {question}</p>', unsafe_allow_html=True)

# Vérifier si le modèle et les embeddings sont chargés
if st.button("Obtenir la réponse"):
    if not st.session_state.model_loaded:
        st.error("Veuillez d'abord charger le modèle depuis la barre latérale.")
    elif not st.session_state.embeddings_loaded:
        st.error("Veuillez d'abord charger les embeddings depuis la barre latérale.")
    else:
        try:
            logger.info(f"Génération de réponse pour la question: '{question}' (profil: {profil_libelle})")

            # Vérifier que toutes les variables nécessaires sont bien dans la session
            logger.info("Vérification des variables de session...")
            if 'embedder' not in st.session_state:
                logger.error("Variable 'embedder' non trouvée dans la session")
                raise ValueError("Variable 'embedder' non trouvée dans la session. Veuillez recharger les embeddings.")
            if 'all_chunks' not in st.session_state:
                logger.error("Variable 'all_chunks' non trouvée dans la session")
                raise ValueError("Variable 'all_chunks' non trouvée dans la session. Veuillez recharger les embeddings.")
            if 'chunk_embeddings' not in st.session_state:
                logger.error("Variable 'chunk_embeddings' non trouvée dans la session")
                raise ValueError("Variable 'chunk_embeddings' non trouvée dans la session. Veuillez recharger les embeddings.")
            if 'model' not in st.session_state:
                logger.error("Variable 'model' non trouvée dans la session")
                raise ValueError("Variable 'model' non trouvée dans la session. Veuillez recharger le modèle.")
            if 'tokenizer' not in st.session_state:
                logger.error("Variable 'tokenizer' non trouvée dans la session")
                raise ValueError("Variable 'tokenizer' non trouvée dans la session. Veuillez recharger le modèle.")

            logger.info("Toutes les variables nécessaires sont présentes dans la session")

            with st.spinner("En cours de réflexion, merci de patienter…"):
                result = answer_question(
                    question,
                    profil_libelle,
                    st.session_state.embedder,
                    st.session_state.all_chunks,
                    st.session_state.chunk_embeddings,
                    st.session_state.model,
                    st.session_state.tokenizer,
                    temperature=temperature,
                    top_p=top_p,
                    top_k=top_k,
                    max_new_tokens=max_new_tokens
                )
            logger.info(f"Réponse générée avec succès en {result['execution_time']:.2f} secondes, {result['token_count']} tokens")

            # Ajouter à l'historique
            st.session_state.question_history.append(question)
            st.session_state.response_history.append(result)
            logger.info("Réponse ajoutée à l'historique")

            # Enregistrer la question et la réponse dans QA.log
            device_name = "GPU" if ('device' in st.session_state and st.session_state.device == 'cuda') else "CPU"
            qa_log_entry = f"""
QUESTION: {question}
PROFIL: {profil_libelle}
RÉPONSE: {result['answer']}
STATS: Tokens générés: {result['token_count']} | Temps d'exécution: {result['execution_time']:.2f}s | Documents consultés: {len(result['retrieved_files'])} | Exécuté sur: {device_name}
-------------------------------------------
"""
            qa_logger.info(qa_log_entry)

            # Afficher la réponse
            st.success("Réponse générée avec succès !")

            # Métriques
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Tokens générés", result["token_count"])
            with col2:
                st.metric("Temps d'exécution", f"{result['execution_time']:.2f} s")
            with col3:
                st.metric("Documents consultés", len(result["retrieved_files"]))
            with col4:
                device_icon = "🚀" if ('device' in st.session_state and st.session_state.device == 'cuda') else "💻"
                device_name = "GPU" if ('device' in st.session_state and st.session_state.device == 'cuda') else "CPU"
                st.metric("Exécuté sur", f"{device_icon} {device_name}")

            # Afficher la réponse formatée
            st.markdown('<div class="response-box">', unsafe_allow_html=True)
            st.markdown("### Réponse")
            st.markdown(result["answer"])
            st.markdown('</div>', unsafe_allow_html=True)

            # Afficher les sources
            with st.expander("Voir les sources"):
                st.markdown("### Documents consultés")
                for file in result["retrieved_files"]:
                    st.markdown(f"- <span class='file-reference'>{file}</span> (disponible sur Sharepoint)", unsafe_allow_html=True)

            # Afficher le contexte
            with st.expander("Voir le contexte complet"):
                st.markdown('<div class="context-box">', unsafe_allow_html=True)
                st.markdown(result["context"])
                st.markdown('</div>', unsafe_allow_html=True)

        except Exception as e:
            error_msg = f"Erreur lors de la génération de la réponse : {e}"
            logger.error(error_msg)
            logger.exception("Détails de l'erreur:")
            st.error(error_msg)

            # Afficher l'état des variables de session pour le débogage
            logger.info("État des variables de session:")
            for key in st.session_state:
                logger.info(f"- {key}: {type(st.session_state[key])}")

# Historique des questions et réponses
if st.session_state.response_history:
    st.markdown('<h2 class="section-title">Historique des questions</h2>', unsafe_allow_html=True)

    for i, (q, r) in enumerate(zip(st.session_state.question_history, st.session_state.response_history)):
        with st.expander(f"Question {i+1}: {q}"):
            st.markdown('<div class="response-box">', unsafe_allow_html=True)
            st.markdown(r["answer"])
            st.markdown('</div>', unsafe_allow_html=True)
            st.caption(f"Générée en {r['execution_time']:.2f} secondes avec {r['token_count']} tokens")

# Footer
st.markdown('<div class="footer">© 2025 Assistant RH RAG - Développé pour la Ville de Pau, CCAS et CAPBP</div>', unsafe_allow_html=True)

# Exécution principale
if __name__ == "__main__":
    # Si le modèle et les embeddings ne sont pas chargés, afficher un message
    if not st.session_state.model_loaded or not st.session_state.embeddings_loaded:
        st.info("Veuillez charger le modèle et les embeddings depuis la barre latérale pour commencer.")
