# -*- coding: utf-8 -*-
"""
Created on Mon May 12 13:40:16 2025

@author: 3D
"""

import json
import logging
import os
from pathlib import Path
import re
import torch
from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline

# Configurer le logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Chemins des fichiers
BASE_PATH = Path(os.getenv("BASE_PATH", "C:/temp/FILE_RAG"))
EMBEDDINGS_CACHE_PATH = BASE_PATH / "embeddings_cache.pt"
CHUNKED_JSON_PATH = BASE_PATH / "output/lint_pdf_chunked_ALL.json"

# Charger le modèle d'embeddings
embedder = SentenceTransformer('distiluse-base-multilingual-cased-v2')

# Charger les chunks à partir du fichier JSON
def load_chunks():
    if not CHUNKED_JSON_PATH.exists():
        logger.error(f"Fichier de chunks introuvable : {CHUNKED_JSON_PATH}")
        return []
    with open(CHUNKED_JSON_PATH, encoding="utf-8") as f:
        return json.load(f)

all_chunks = load_chunks()

# Charger ou calculer les embeddings des chunks
if EMBEDDINGS_CACHE_PATH.exists():
    chunk_embeddings = torch.load(EMBEDDINGS_CACHE_PATH)
    if len(chunk_embeddings) != len(all_chunks):
        logger.warning("Cache mismatch, recomputing embeddings")
        chunk_texts = [chunk["text"] for chunk in all_chunks]
        chunk_embeddings = embedder.encode(chunk_texts, convert_to_tensor=True)
        torch.save(chunk_embeddings, EMBEDDINGS_CACHE_PATH)
else:
    chunk_texts = [chunk["text"] for chunk in all_chunks]
    chunk_embeddings = embedder.encode(chunk_texts, convert_to_tensor=True)
    torch.save(chunk_embeddings, EMBEDDINGS_CACHE_PATH)

# Charger le modèle et le tokenizer
MODEL_NAME = "mistralai/Mistral-7B-Instruct-v0.3"
HF_TOKEN = "*************************************"  # Remplace par ton token Hugging Face

logger.info("Chargement du modèle et du tokenizer...")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME, token=HF_TOKEN)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    token=HF_TOKEN,
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto"
)

def format_context_with_filenames(chunks):
    """Formate le contexte avec noms de fichiers et IDs."""
    context = ""
    for chunk in chunks:
        context += f"Fichier: {chunk['file_name']} (chunk {chunk['chunk_id']}):\n{chunk['text']}\n\n"
    return context.strip()

def answer_question(
    question,
    profil_libelle,
    temperature=0.2,
    top_p=0.85,
    top_k=100,
    max_new_tokens=800
):
    # Calculer l'embedding de la question
    question_embedding = embedder.encode(question, convert_to_tensor=True)
    
    # Calculer les similarités cosinus
    similarities = torch.cosine_similarity(question_embedding, chunk_embeddings)
    
    # Récupérer les indices des top-3 chunks
    top_k_indices = torch.topk(similarities, k=3).indices
    
    # Récupérer les top-3 chunks
    retrieved_chunks = [all_chunks[i] for i in top_k_indices]
    
    # Nettoyer les chunks (virer <!-- image -->)
    cleaned_chunks = []
    for chunk in retrieved_chunks:
        text = chunk["text"]
        text = re.sub(r'<!-- image -->', '', text)
        cleaned_chunks.append({
            "file_name": chunk["file_name"],
            "chunk_id": chunk["chunk_id"],
            "text": text
        })
    
    # Formater le contexte
    formatted_context = format_context_with_filenames(cleaned_chunks)
    
    # Créer le prompt
    prompt = f"""Tu es un expert RH pour les agents de la Ville de Pau, du CCAS ou de la CAPBP.
    Ton rôle est de fournir des informations précises et adaptées sur les droits et procédures (déplacements, frais, primes, maladie, accidents de service, etc.).

Profil de l'agent : {profil_libelle}

Contexte pertinent :
{formatted_context}

Question : {question}

Réponds en français, de manière claire et synthétique.
 Base ta réponse uniquement sur le contexte fourni et adapte-la au profil de l'agent.
 Si une information est manquante, indique-le clairement.
 Évite les redondances
 Structure ta réponse avec des puces ou des paragraphes courts, et cite les fichiers PDF pertinents du contexte.
 informe que les fichiers sont dans Sharepoint 
 

Réponse :
"""
    
    # Générer la réponse
    pipe = pipeline(
        "text-generation",
        model=model,
        tokenizer=tokenizer,
        return_full_text=False
    )
    result = pipe(
        prompt,
        max_new_tokens=max_new_tokens,
        temperature=temperature,
        top_p=top_p,
        top_k=top_k,
        do_sample=True
    )
    answer = result[0]['generated_text'].strip()
    
    # Compter les tokens
    encoded = tokenizer(answer, return_tensors="pt")
    token_count = encoded.input_ids.shape[-1]
    logger.info(f"Réponse générée avec {token_count} tokens.")
    
    return f"{answer}\n\n---\n[⏱ Réponse générée en {token_count} tokens]"

# Exemple d'utilisation
if __name__ == "__main__":
    # question = "Que faire en cas d’accident de service ?"
    # profil = "CAPBP"  
    # print(f"Q:  {question}        A: {profil}")
    # response = answer_question(question, profil)
    # print(response)
    # print('------------------------------------------------------')
    # question = "Que faire en cas d’accident de service ?"
    # profil = "CCAS"
    # response = answer_question(question, profil)
    # print(response)
    print('------------------------------------------------------')
    question = "je vais à Paris en avion pour une formation demandé par la CAPBP je dors deux nuits a l'hotel et je passe trois jours sur place: comment je suis remboursé de mes frais, comment je prepare le déplacement??"
    profil = "CAPBP"
    response = answer_question(question, profil)
    print(response)
    print('------------------------------------------------------')
    question = "est ce que je suis aidé si je vien au travail en vélo ?"
    profil = "CAPBP"
    response = answer_question(question, profil)
    print(response)
    print('------------------------------------------------------')
    question = "J'ai deux enfant de 8 et 15 ans ais-je le droit  à une prime? Si oui combien je suis categorie B"
    profil = "CAPBP"
    response = answer_question(question, profil)
    print(response)

    print('------------------------------------------------------')
    question = "je fais greve une semaine est-ce que cela coupe mon salire, de combien?"
    profil = "CAPBP"
    response = answer_question(question, profil)
    print(response)
    print('------------------------------------------------------')
    question = "quels son les droit et devoir de fonctionnaires dans la CAPBP??"
    profil = "CAPBP"
    response = answer_question(question, profil)
    print(response)
    